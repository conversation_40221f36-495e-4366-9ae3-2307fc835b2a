"""
Subscription routes for ReplyPal API
Handles subscription creation, management, and webhooks
"""

import os
from typing import Dict, Optional, List, Any

import stripe
from fastapi import APIRouter, Depends, Request, HTTPException, status, Header
from fastapi.responses import JSONResponse, RedirectResponse

from ..auth import get_current_user, User
from ..models import SubscriptionTier
from . import stripe_service as stripe_svc
from .. import database as db
from .settings import AUTO_RENEWAL_TOKEN_THRESHOLD, AUTO_RENEWAL_TIME_THRESHOLD_DAYS

router = APIRouter(prefix="/subscription", tags=["Subscription"])

# Stripe configuration
STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY")
STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")
FRONTEND_SUCCESS_URL = os.getenv("FRONTEND_SUCCESS_URL", "http://localhost:3000/success")
FRONTEND_CANCEL_URL = os.getenv("FRONTEND_CANCEL_URL", "http://localhost:3000/cancel")

# Initialize Stripe
stripe.api_key = STRIPE_SECRET_KEY


@router.get("/publishable-key", response_model=Dict[str, str])
async def get_publishable_key():
    """Get Stripe publishable key"""
    return {"publishable_key": STRIPE_PUBLISHABLE_KEY}


@router.get("/plans", response_model=List[Dict[str, Any]])
async def get_subscription_plans():
    """Get available subscription plans"""
    plans = [
        {
            "id": "free",
            "name": "Free",
            "description": "Basic access with limited features",
            "price": 0,
            "currency": "usd",
            "interval": "month",
            "features": [
                "5 requests per day",
                "500 tokens per request",
                "Basic support"
            ],
            "stripe_price_id": None
        },
        {
            "id": "basic",
            "name": "Basic",
            "description": "Standard access for individual users",
            "price": 9.99,
            "currency": "usd",
            "interval": "month",
            "features": [
                "50 requests per day",
                "1,000 tokens per request",
                "Email support",
                "History saving"
            ],
            "stripe_price_id": os.getenv("STRIPE_BASIC_PRICE_ID")
        }
    ]

    return plans


@router.get("/customer", response_model=Dict[str, Any])
async def get_customer_info(current_user: User = Depends(get_current_user)):
    """Get customer information for the current user"""
    # Get user subscription
    user_subscription = await db.get_user_subscription(current_user.uid)
    if not user_subscription:
        # Create Stripe customer if not exists
        await stripe_svc.create_stripe_customer(
            user_id=current_user.uid,
            email=current_user.email,
            name=current_user.name
        )
        user_subscription = await db.get_user_subscription(current_user.uid)

    # Get Stripe customer
    stripe_customer = await db.get_stripe_customer(user_subscription["stripe_customer_id"])

    # Get subscription details if exists
    subscription_details = None
    if user_subscription.get("stripe_subscription_id"):
        subscription = await db.get_stripe_subscription(user_subscription["stripe_subscription_id"])
        if subscription:
            subscription_details = {
                "id": subscription["id"],
                "status": subscription["status"],
                "tier": subscription["tier"],
                "current_period_end": subscription["current_period_end"],
                "cancel_at_period_end": subscription["cancel_at_period_end"]
            }

    return {
        "customer_id": stripe_customer["id"],
        "email": stripe_customer["email"],
        "name": stripe_customer["name"],
        "subscription": subscription_details,
        "tier": user_subscription["tier"],
        "status": user_subscription["status"],
        "usage_limits": user_subscription["usage_limits"]
    }


@router.post("/create-checkout-session", response_model=Dict[str, Any])
async def create_checkout_session(
    price_id: str,
    current_user: User = Depends(get_current_user)
):
    """Create a Stripe Checkout session for subscription"""
    try:
        # Get or create Stripe customer
        user_subscription = await db.get_user_subscription(current_user.uid)
        if not user_subscription:
            customer = await stripe_svc.create_stripe_customer(
                user_id=current_user.uid,
                email=current_user.email,
                name=current_user.name
            )
            customer_id = customer.id
        else:
            customer_id = user_subscription["stripe_customer_id"]

        # Create checkout session
        checkout_session = stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=["card"],
            line_items=[{
                "price": price_id,
                "quantity": 1
            }],
            mode="subscription",
            success_url=f"{FRONTEND_SUCCESS_URL}?session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=FRONTEND_CANCEL_URL,
            metadata={
                "user_id": current_user.uid
            }
        )

        return {"checkout_url": checkout_session.url}

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating checkout session: {str(e)}"
        )


@router.post("/customer-portal", response_model=Dict[str, Any])
async def create_customer_portal_session(
    current_user: User = Depends(get_current_user)
):
    """Create a Stripe Customer Portal session for subscription management"""
    try:
        # Get user subscription
        user_subscription = await db.get_user_subscription(current_user.uid)
        if not user_subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No subscription found for this user"
            )

        # Create customer portal session
        portal_session = stripe.billing_portal.Session.create(
            customer=user_subscription["stripe_customer_id"],
            return_url=FRONTEND_SUCCESS_URL
        )

        return {"portal_url": portal_session.url}

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating customer portal session: {str(e)}"
        )


@router.post("/webhook", response_model=Dict[str, Any])
async def stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None)
):
    """Handle Stripe webhook events"""
    if not stripe_signature:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing Stripe signature"
        )

    # Get request body
    payload = await request.body()

    # Handle webhook event
    result = await stripe_svc.handle_webhook_event(payload, stripe_signature)

    return result


@router.post("/cancel", response_model=Dict[str, Any])
async def cancel_subscription(
    current_user: User = Depends(get_current_user)
):
    """Cancel the current user's subscription"""
    # Get user subscription
    user_subscription = await db.get_user_subscription(current_user.uid)
    if not user_subscription or not user_subscription.get("stripe_subscription_id"):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found for this user"
        )

    # Cancel subscription
    result = await stripe_svc.cancel_subscription(user_subscription["stripe_subscription_id"])

    return result


@router.get("/usage", response_model=Dict[str, Any])
async def get_usage_stats(
    current_user: User = Depends(get_current_user)
):
    """Get usage statistics for the current user"""
    # Get daily usage count
    daily_usage = await db.get_daily_usage_count(current_user.uid)

    # Get user subscription for limits
    user_subscription = await db.get_user_subscription(current_user.uid)
    if not user_subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No subscription found for this user"
        )

    # Calculate usage percentage
    daily_limit = user_subscription.get("usage_limits", {}).get("requests_per_day", 0)
    usage_percentage = (daily_usage / daily_limit) * 100 if daily_limit > 0 else 0

    return {
        "daily_usage": daily_usage,
        "daily_limit": daily_limit,
        "usage_percentage": usage_percentage,
        "max_tokens": user_subscription.get("usage_limits", {}).get("max_tokens", 0)
    }


@router.get("/token-usage", response_model=Dict[str, Any])
async def get_token_usage(
    current_user: User = Depends(get_current_user)
):
    """Get token usage and auto-renewal status for the current user"""
    # Get user subscription
    user_subscription = await db.get_user_subscription(current_user.uid)
    if not user_subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No subscription found for this user"
        )

    # Get total tokens used
    total_tokens_used = user_subscription.get("total_tokens_used", 0)

    # Get last renewal date
    last_renewal_date = user_subscription.get("last_renewal_date")

    # Calculate days until next renewal (if on Basic plan)
    days_until_renewal = None
    auto_renewal_enabled = False

    if user_subscription.get("tier") == SubscriptionTier.BASIC.value:
        auto_renewal_enabled = True

        # Convert ISO string to datetime if needed
        if last_renewal_date:
            if isinstance(last_renewal_date, str):
                try:
                    from dateutil import parser
                    last_renewal_date = parser.parse(last_renewal_date)
                except Exception as e:
                    print(f"Error parsing last_renewal_date: {str(e)}")
                    last_renewal_date = None

            if last_renewal_date:
                from datetime import datetime, timedelta
                next_renewal_date = last_renewal_date + timedelta(days=AUTO_RENEWAL_TIME_THRESHOLD_DAYS)
                days_until_renewal = (next_renewal_date - datetime.now()).days

    # Calculate tokens until renewal threshold
    tokens_until_threshold = AUTO_RENEWAL_TOKEN_THRESHOLD - total_tokens_used if total_tokens_used < AUTO_RENEWAL_TOKEN_THRESHOLD else 0

    return {
        "total_tokens_used": total_tokens_used,
        "tokens_threshold": AUTO_RENEWAL_TOKEN_THRESHOLD,
        "tokens_until_threshold": tokens_until_threshold,
        "tokens_percentage": (total_tokens_used / AUTO_RENEWAL_TOKEN_THRESHOLD) * 100 if total_tokens_used < AUTO_RENEWAL_TOKEN_THRESHOLD else 100,
        "last_renewal_date": last_renewal_date,
        "days_until_renewal": days_until_renewal,
        "auto_renewal_enabled": auto_renewal_enabled,
        "time_threshold_days": AUTO_RENEWAL_TIME_THRESHOLD_DAYS,
        "tier": user_subscription.get("tier")
    }
