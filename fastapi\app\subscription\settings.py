"""
Subscription settings for ReplyPal API
Contains configuration for subscription tiers, limits, and auto-renewal
"""

import os
from models import UsageLimit, SubscriptionTier

# Token threshold for auto-renewal (default: 1500)
AUTO_RENEWAL_TOKEN_THRESHOLD = int(os.getenv("AUTO_RENEWAL_TOKEN_THRESHOLD", "1500"))

# Time threshold for auto-renewal in days (default: 30 days)
AUTO_RENEWAL_TIME_THRESHOLD_DAYS = int(os.getenv("AUTO_RENEWAL_TIME_THRESHOLD_DAYS", "30"))

# Stripe price ID for Basic plan
STRIPE_BASIC_PRICE_ID = os.getenv("STRIPE_BASIC_PRICE_ID", "price_1RMu1WQkxr8FpV1iMFo6eiQA")

# Usage limits for each tier
TIER_LIMITS = {
    SubscriptionTier.FREE: UsageLimit(requests_per_day=5, max_tokens=500),
    SubscriptionTier.BASIC: UsageLimit(requests_per_day=50, max_tokens=1000)
}

# Print subscription settings for debugging
print("=" * 50)
print("SUBSCRIPTION SETTINGS:")
print(f"Auto-Renewal Token Threshold: {AUTO_RENEWAL_TOKEN_THRESHOLD}")
print(f"Auto-Renewal Time Threshold: {AUTO_RENEWAL_TIME_THRESHOLD_DAYS} days")
print(f"Stripe Basic Price ID: {STRIPE_BASIC_PRICE_ID}")
print(f"Free Tier Limits: {TIER_LIMITS[SubscriptionTier.FREE]}")
print(f"Basic Tier Limits: {TIER_LIMITS[SubscriptionTier.BASIC]}")
print("=" * 50)
